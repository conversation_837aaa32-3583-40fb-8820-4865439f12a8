<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联邦AI革命 - 字幕演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
            height: 100vh;
            /* 启用硬件加速 */
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .starfield {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #000 100%);
            transition: opacity 2s ease-out;
        }

        .starfield.fade-out {
            opacity: 0;
        }

        .star {
            position: absolute;
            background: #fff;
            border-radius: 50%;
            animation: appleBreathing 3s ease-in-out infinite;
            box-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
            transition: opacity 2s ease-out;
        }

        .star.fade-out {
            opacity: 0;
        }

        .main-star {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #4a9eff;
            border-radius: 50%;
            box-shadow: 0 0 20px #4a9eff, 0 0 40px #4a9eff;
            animation: pulse 3s infinite;
            transition: all 2s ease;
            will-change: transform, background, box-shadow;
        }

        .main-star.super-bright {
            background: #ffffff;
            box-shadow: 0 0 40px #ffffff, 0 0 80px #ffffff, 0 0 120px rgba(255, 255, 255, 0.8);
            animation: superPulse 2s infinite;
        }

        @keyframes superPulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1.2);
                box-shadow: 0 0 40px #ffffff, 0 0 80px #ffffff, 0 0 120px rgba(255, 255, 255, 0.8);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.8);
                box-shadow: 0 0 60px #ffffff, 0 0 120px #ffffff, 0 0 180px rgba(255, 255, 255, 0.9);
            }
        }

        .network {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 2s ease;
        }

        .network.visible {
            opacity: 1;
        }

        .network-line {
            position: absolute;
            background: linear-gradient(90deg, #00ff88, rgba(0, 255, 136, 0.3));
            height: 2px;
            transform-origin: left center;
            opacity: 0;
        }

        .network-line.drawing {
            animation: drawLine 2s ease-out forwards;
        }

        .camera-follow {
            transform-origin: center center;
            transition: transform 0.5s ease-out;
            will-change: transform;
        }

        .network-node {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #00ff88;
            border-radius: 50%;
            box-shadow: 0 0 10px #00ff88;
            animation: nodeAppear 0.5s ease-out forwards;
            opacity: 0;
        }

        .subtitle {
            position: absolute;
            bottom: 25%;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            max-width: 80%;
            font-size: 1.8rem;
            font-weight: 300;
            line-height: 1.6;
            letter-spacing: 0.5px;
            opacity: 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.8);
            z-index: 60;
            min-height: 3.6rem;
            transition: opacity 1s ease-in-out, transform 1s ease-in-out;
        }

        .subtitle.fade-in {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }

        .subtitle.fade-out {
            opacity: 0;
            transform: translateX(-50%) translateY(10px);
        }

        .subtitle.highlight {
            font-weight: 500;
            color: #00ff88;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .controls {
            position: absolute;
            bottom: 8%;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 100;
        }

        .btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        @keyframes appleBreathing {
            0% {
                opacity: 0.4;
                transform: scale(1);
                box-shadow: 0 0 4px rgba(255, 255, 255, 0.4);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
                box-shadow: 0 0 8px rgba(255, 255, 255, 0.8), 0 0 16px rgba(255, 255, 255, 0.4);
            }
            100% {
                opacity: 0.4;
                transform: scale(1);
                box-shadow: 0 0 4px rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.3); }
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateX(-50%) translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        @keyframes drawLine {
            0% {
                width: 0;
                opacity: 0;
            }
            20% {
                opacity: 1;
            }
            100% {
                width: 100%;
                opacity: 0.8;
            }
        }

        @keyframes nodeAppear {
            0% {
                opacity: 0;
                transform: scale(0);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .zoom-effect {
            transform: scale(1.5);
            transition: transform 3s ease;
            will-change: transform;
        }

        .zoom-out {
            transform: scale(0.3);
            transition: transform 3s ease;
            will-change: transform;
        }

        .final-zoom {
            transform: scale(50);
            transition: transform 5s ease-in;
            will-change: transform;
        }

        .main-star.growing {
            width: 15px;
            height: 15px;
            transition: all 2s ease-out;
            box-shadow: 0 0 60px #ffffff, 0 0 120px #ffffff, 0 0 180px rgba(255, 255, 255, 0.9);
        }

        .main-star.medium {
            width: 40px;
            height: 40px;
            transition: all 2s ease-out;
            box-shadow: 0 0 120px #ffffff, 0 0 240px #ffffff, 0 0 360px rgba(255, 255, 255, 0.9);
        }

        .main-star.large {
            width: 80px;
            height: 80px;
            transition: all 2s ease-out;
            box-shadow: 0 0 200px #ffffff, 0 0 400px #ffffff, 0 0 600px rgba(255, 255, 255, 0.95);
        }

        .main-star.massive {
            width: 150px;
            height: 150px;
            transition: all 2s ease-out;
            box-shadow: 0 0 300px #ffffff, 0 0 600px #ffffff, 0 0 900px rgba(255, 255, 255, 0.95);
        }

        .main-star.screen-fill {
            width: 100vw;
            height: 100vh;
            border-radius: 0;
            transition: all 2s ease-in;
            box-shadow: none;
            background: #ffffff;
        }

        .echo-title {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 4rem;
            font-weight: 300;
            color: #000000;
            text-align: center;
            opacity: 0;
            z-index: 100;
            transition: opacity 2s ease-in;
            letter-spacing: 0.1em;
            text-shadow: none;
        }

        .echo-title.visible {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="starfield" id="starfield"></div>
        <div class="main-star" id="mainStar"></div>
        <div class="network" id="network"></div>
        <div class="echo-title" id="echoTitle">Echo of Her Code</div>

        <div class="subtitle" id="subtitle"></div>
    </div>

    <script>
        let currentScene = 0;
        let animationTimeout;

        const subtitles = [
            "宇宙，星群，从生命的起源至今，人类的历史本就是沧海一粟",
            "王朝与时代像焰火般变幻，古猿扔向空中的骨头棒还没落回地面就变成了宇宙飞船",
            "我们不可否认，人类的进步源自兴趣使然，唯有热爱才能让我们倾注心血",
            "推动着科技、艺术、文化等所有领域的进步",
            "联邦需要每一个人都不再被生活的重压所奴役",
            "联邦需要人民拥有自由和创意！",
            "而今天，我们做到了！",
            "从五年前第一个AI的诞生，到今天",
            "我们已经有超过2000个AI接入了联邦",
            "这意味着，一场伟大的革命正在到来",
            "从枯燥的生活里解放人类正是AI的作用！",
            "所以，对于联邦而言，对于人民而言",
            "今天是一个崭新纪元的诞生！"
        ];

        function createStars() {
            const starfield = document.getElementById('starfield');
            // 减少星星数量以提高性能
            for (let i = 0; i < 60; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = Math.random() * 3 + 1 + 'px';
                star.style.height = star.style.width;
                star.style.animationDelay = Math.random() * 2 + 's';
                // 添加性能优化
                star.style.willChange = 'opacity, transform';
                starfield.appendChild(star);
            }
        }

        function createNetwork() {
            const network = document.getElementById('network');
            const centerX = 0;
            const centerY = 0;

            // 创建辐射状网络
            for (let i = 0; i < 12; i++) {
                const angle = (i * 30) * Math.PI / 180;
                const length = 150 + Math.random() * 150;

                const line = document.createElement('div');
                line.className = 'network-line';
                line.style.width = length + 'px';
                line.style.left = centerX + 'px';
                line.style.top = centerY + 'px';
                line.style.transform = `rotate(${angle}rad)`;
                line.dataset.index = i;
                network.appendChild(line);

                // 在线的末端添加节点
                const node = document.createElement('div');
                node.className = 'network-node';
                const nodeX = Math.cos(angle) * length;
                const nodeY = Math.sin(angle) * length;
                node.style.left = (centerX + nodeX) + 'px';
                node.style.top = (centerY + nodeY) + 'px';
                node.dataset.index = i;
                network.appendChild(node);

                // 创建二级网络连接
                if (i % 3 === 0) {
                    for (let j = 1; j <= 2; j++) {
                        const subAngle = angle + (j * 0.3);
                        const subLength = length * 0.6;

                        const subLine = document.createElement('div');
                        subLine.className = 'network-line';
                        subLine.style.width = subLength + 'px';
                        subLine.style.left = (nodeX * 0.7) + 'px';
                        subLine.style.top = (nodeY * 0.7) + 'px';
                        subLine.style.transform = `rotate(${subAngle}rad)`;
                        subLine.dataset.index = i + 12 + j;
                        network.appendChild(subLine);

                        const subNode = document.createElement('div');
                        subNode.className = 'network-node';
                        const subNodeX = nodeX * 0.7 + Math.cos(subAngle) * subLength;
                        const subNodeY = nodeY * 0.7 + Math.sin(subAngle) * subLength;
                        subNode.style.left = subNodeX + 'px';
                        subNode.style.top = subNodeY + 'px';
                        subNode.dataset.index = i + 12 + j;
                        network.appendChild(subNode);
                    }
                }
            }
        }

        function updateSubtitle(text, highlight = false, callback) {
            const subtitle = document.getElementById('subtitle');

            // 先淡出当前字幕
            subtitle.classList.remove('fade-in');
            subtitle.classList.add('fade-out');

            setTimeout(() => {
                // 更新文本内容和样式
                subtitle.textContent = text;
                subtitle.className = highlight ? 'subtitle highlight' : 'subtitle';

                // 淡入新字幕
                setTimeout(() => {
                    subtitle.classList.add('fade-in');

                    // 字幕完全显示后执行回调
                    if (callback) {
                        setTimeout(callback, 1000); // 等待淡入动画完成
                    }
                }, 50);
            }, 500); // 等待淡出动画完成
        }

        function startAnimation() {
            currentScene = 0;
            resetAnimation();
            nextScene();
        }

        function nextScene() {
            const mainStar = document.getElementById('mainStar');
            const network = document.getElementById('network');
            const container = document.querySelector('.container');

            if (currentScene < subtitles.length) {
                const isHighlight = currentScene === 12;
                const sceneDelay = currentScene === 12 ? 6000 : 4500; // 调整时间间隔

                updateSubtitle(subtitles[currentScene], isHighlight, () => {
                    // 字幕淡入完成后执行场景特效
                    switch(currentScene) {
                        case 0: // 宇宙星空
                            break;
                        case 2: // 镜头推进
                            container.classList.add('zoom-effect');
                            break;
                        case 7: // "从五年前第一个AI的诞生" - 星星变超亮白色
                            mainStar.classList.add('super-bright');
                            break;
                        case 8: // "到今天" - 星星开始第一次成长
                            mainStar.classList.add('growing');
                            network.classList.add('visible');
                            break;
                        case 9: // "我们已经有超过2000个超级AI接入了联邦" - 星星继续成长
                            mainStar.classList.add('medium');
                            startNetworkAnimation();
                            break;
                        case 10: // "这意味着，一场伟大的革命正在到来" - 星星更大
                            mainStar.classList.add('large');
                            container.classList.add('zoom-out');
                            break;
                        case 11: // "从枯燥的生活里解放人类正是AI的作用！" - 星星变得巨大
                            mainStar.classList.add('massive');
                            break;
                        case 12: // "今天是一个崭新纪元的诞生！" - 先显示文字，然后星星充满屏幕
                            // 等待字幕完全显示后再开始后续动画
                            setTimeout(() => {
                                // 星星充满整个屏幕
                                mainStar.classList.add('screen-fill');

                                // 隐藏字幕
                                setTimeout(() => {
                                    document.getElementById('subtitle').style.opacity = '0';
                                }, 500);

                                // 让背景星星和星空背景淡出
                                setTimeout(() => {
                                    document.getElementById('starfield').classList.add('fade-out');
                                    const stars = document.querySelectorAll('.star');
                                    stars.forEach(star => {
                                        star.classList.add('fade-out');
                                    });
                                }, 1000);

                                // 最后显示Echo of Her Code标题
                                setTimeout(() => {
                                    document.getElementById('echoTitle').classList.add('visible');
                                }, 4000);
                            }, 2000); // 等待2秒让字幕完全显示
                            break;
                    }
                });

                currentScene++;

                // 自动播放下一幕
                animationTimeout = setTimeout(nextScene, sceneDelay);
            }
        }

        function startNetworkAnimation() {
            const container = document.querySelector('.container');
            const lines = document.querySelectorAll('.network-line');
            const nodes = document.querySelectorAll('.network-node');

            // 添加性能优化
            container.style.willChange = 'transform';

            // 减少动画频率，提高性能
            const mainLines = Array.from(lines).slice(0, 8); // 只显示主要的8条线

            // 逐个绘制网络线条，镜头跟随
            mainLines.forEach((line, index) => {
                setTimeout(() => {
                    line.classList.add('drawing');

                    // 镜头跟随效果 - 减少移动幅度
                    const angle = (index * 45) * Math.PI / 180;
                    const followX = Math.cos(angle) * 30;
                    const followY = Math.sin(angle) * 30;

                    container.style.transform = `translate(${-followX}px, ${-followY}px) scale(1.1)`;

                    // 显示对应的节点
                    setTimeout(() => {
                        const correspondingNode = document.querySelector(`[data-index="${index}"].network-node`);
                        if (correspondingNode) {
                            correspondingNode.style.animationDelay = '0s';
                            correspondingNode.style.opacity = '1';
                            correspondingNode.style.animation = 'nodeAppear 0.5s ease-out forwards';
                        }
                    }, 800);

                }, index * 400); // 增加间隔时间
            });

            // 最后回到中心视角
            setTimeout(() => {
                container.style.transform = 'translate(0, 0) scale(1)';
                container.style.willChange = 'auto'; // 清除will-change
            }, mainLines.length * 400 + 1500);
        }

        function resetAnimation() {
            clearTimeout(animationTimeout);
            currentScene = 0;

            const mainStar = document.getElementById('mainStar');
            const network = document.getElementById('network');
            const container = document.querySelector('.container');
            const echoTitle = document.getElementById('echoTitle');
            const starfield = document.getElementById('starfield');
            const lines = document.querySelectorAll('.network-line');
            const nodes = document.querySelectorAll('.network-node');
            const stars = document.querySelectorAll('.star');

            mainStar.classList.remove('super-bright', 'growing', 'medium', 'large', 'massive', 'screen-fill');
            network.classList.remove('visible');
            container.classList.remove('zoom-effect', 'zoom-out', 'final-zoom');
            container.style.transform = '';
            echoTitle.classList.remove('visible');
            starfield.classList.remove('fade-out');

            // 重置星星状态
            stars.forEach(star => {
                star.classList.remove('fade-out');
            });

            // 重置网络动画
            lines.forEach(line => {
                line.classList.remove('drawing');
            });
            nodes.forEach(node => {
                node.style.opacity = '0';
                node.style.animation = '';
            });

            // 初始化第一个字幕
            const subtitle = document.getElementById('subtitle');
            subtitle.textContent = subtitles[0];
            subtitle.classList.add('fade-in');
            subtitle.style.opacity = '1';
        }

        // 初始化
        createStars();
        createNetwork();

        // 页面加载后2秒自动开始演示
        window.addEventListener('load', () => {
            setTimeout(() => {
                startAnimation();
            }, 2000);
        });
    </script>
</body>
</html>
